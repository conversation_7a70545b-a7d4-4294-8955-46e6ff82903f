package com.natamus.collective.globalcallbacks;

import com.mojang.blaze3d.vertex.PoseStack;
import com.natamus.collective.implementations.event.Event;
import com.natamus.collective.implementations.event.EventFactory;
import net.minecraft.client.renderer.MultiBufferSource;

public class CollectiveWorldRenderCallback {
    private CollectiveWorldRenderCallback() { }

    public static final Event<On_World_Render> ON_WORLD_RENDER = EventFactory.createArrayBacked(On_World_Render.class, callbacks -> (poseStack, bufferSource, camX, camY, camZ) -> {
        for (On_World_Render callback : callbacks) {
            callback.onWorldRender(poseStack, bufferSource, camX, camY, camZ);
        }
        
        // Call enhancement rendering
        try {
            Class<?> renderEnhancementClass = Class.forName("com.natamus.collective.features.RenderEnhancement");
            renderEnhancementClass.getMethod("onWorldRender", PoseStack.class, MultiBufferSource.class, double.class, double.class, double.class)
                .invoke(null, poseStack, bufferSource, camX, camY, camZ);
        } catch (Exception ignored) {
            // Enhancement features not available
        }
    });

    @FunctionalInterface
    public interface On_World_Render {
         void onWorldRender(PoseStack poseStack, MultiBufferSource bufferSource, double camX, double camY, double camZ);
    }
}
