{"schemaVersion": 1, "id": "collective", "version": "8.3", "name": "Collective", "description": "Collective is a shared library with common code for all of Serilum's mods.", "authors": ["<PERSON>"], "contact": {"homepage": "https://curseforge.com/minecraft/mc-mods/collective", "sources": "https://github.com/Serilum/Collective", "issues": "https://github.com/Serilum/.issue-tracker/labels/Library: Collective"}, "license": "All Rights Reserved", "icon": "icon.png", "environment": "*", "entrypoints": {"main": ["com.natamus.collective.CollectiveFabric"], "client": ["com.natamus.collective.CollectiveFabricClient"], "modmenu": ["com.natamus.collective.fabric.config.FabricCollectiveConfigScreen"]}, "mixins": ["collective_fabric.mixins.json", "collective_fabric.crop.mixins.json"], "accessWidener": "collective.accesswidener", "depends": {"fabricloader": ">=0.15.0", "fabric-api": "*", "minecraft": ">=1.21", "java": ">=21"}, "custom": {"modmenu": {"links": {"Donate <3": "https://serilum.com/donate"}, "badges": ["library"]}}, "contributors": [" ", "Thank you very much to my Membership sponsors for supporting the development of this mod!", " ", "If you would like to contribute too, check out https://serilum.com/donate.", " ", "Membership sponsors:", " <PERSON><PERSON>", " <PERSON><PERSON>", " aternosorg", " Cherriesaurus", " <PERSON>", " t128", " <PERSON>", " XAgent"]}