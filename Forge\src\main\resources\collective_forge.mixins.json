{"required": true, "minVersion": "0.8", "package": "com.natamus.collective.forge.mixin", "plugin": "com.natamus.collective.forge.mixin.plugin.ForgeMixinConfigPlugin", "compatibilityLevel": "JAVA_17", "mixins": ["BlockEntityMixin", "BoneMealItemMixin", "PrimaryLevelDataMixin"], "client": ["ClientPacketListenerMixin", "G<PERSON><PERSON><PERSON><PERSON>", "TitleScreenMixin", "WorldOpenFlowsMixin"], "server": [], "injectors": {"defaultRequire": 1}}