plugins {
    id "org.jetbrains.gradle.plugin.idea-ext" version "1.1.7"
}

subprojects {
    apply plugin: 'java'

    java.toolchain.languageVersion = JavaLanguageVersion.of(21)
    java.withSourcesJar()
    java.withJavadocJar()
        
    jar {
        manifest {
            attributes([
                'Specification-Title'     : mod_name,
                'Specification-Vendor'    : mod_author,
                'Specification-Version'   : project.jar.archiveVersion,
                'Implementation-Title'    : project.name,
                'Implementation-Version'  : project.jar.archiveVersion,
                'Implementation-Vendor'   : mod_author,
                'Implementation-Timestamp': new Date().format("yyyy-MM-dd'T'HH:mm:ssZ"),
                'Timestamp'               : System.currentTimeMillis(),
                'Built-On-Java'           : "${System.getProperty('java.vm.version')} (${System.getProperty('java.vm.vendor')})",
                'Built-On-Minecraft'      : minecraft_version
            ])
        }
    }

    repositories {
        mavenCentral()
        maven {
            name = 'Sponge / Mixin'
            url = 'https://repo.spongepowered.org/repository/maven-public/'
        }
        maven {
            name = 'BlameJared Maven (JEI / CraftTweaker / Bookshelf)'
            url = 'https://maven.blamejared.com'
        }
    }

    tasks.withType(JavaCompile).configureEach {
    
        it.options.encoding = 'UTF-8'
        it.options.getRelease().set(21)
    }

    tasks.withType(GenerateModuleMetadata).configureEach {

        enabled = false
    }

    tasks.withType(Jar).all {
        
        duplicatesStrategy 'include'
    }
}

idea {
    module {
        downloadSources = true
        downloadJavadoc = true
    }
}