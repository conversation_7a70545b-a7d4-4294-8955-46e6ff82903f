{"required": true, "minVersion": "0.8", "package": "com.natamus.collective.neoforge.mixin", "plugin": "com.natamus.collective.neoforge.mixin.plugin.NeoForgeMixinConfigPlugin", "compatibilityLevel": "JAVA_21", "mixins": ["BaseSpawnerMixin", "BlockEntityMixin", "BoneMealItemMixin", "PrimaryLevelDataMixin"], "client": ["ClientPacketListenerMixin", "G<PERSON><PERSON><PERSON><PERSON>", "TitleScreenMixin", "WorldOpenFlowsMixin"], "server": [], "injectors": {"defaultRequire": 1}}