{"version": "0.2.0", "configurations": [{"type": "java", "request": "launch", "name": "NeoForge - Client", "presentation": {"group": "NG - NeoForge", "order": 0}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--launchTarget", "neoforgeclientdev", "--version", "21.5.2-beta", "--assetIndex", "asset-index", "--assetsDir", "C:\\Users\\<USER>\\.gradle\\caches\\minecraft\\assets", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-Dforge.enabledGameTestNamespaces=collective", "-Dneoforge.enableGameTest=true", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathClient\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\client", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}, {"type": "java", "request": "launch", "name": "NeoForge - ClientData", "presentation": {"group": "NG - NeoForge", "order": 1}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--launchTarget", "neoforgeclientdatadev", "--assetIndex", "asset-index", "--assetsDir", "C:\\Users\\<USER>\\.gradle\\caches\\minecraft\\assets", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathClientData\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\clientData", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}, {"type": "java", "request": "launch", "name": "NeoForge - GameTestServer", "presentation": {"group": "NG - NeoForge", "order": 2}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--launchTarget", "neoforgegametestserverdev", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-Dforge.enabledGameTestNamespaces=collective", "-Dneoforge.enableGameTest=true", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathGameTestServer\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\gameTestServer", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}, {"type": "java", "request": "launch", "name": "NeoForge - Junit", "presentation": {"group": "NG - NeoForge", "order": 3}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--launchTarget", "neoforgejunitdev", "--version", "21.5.2-beta", "--assetIndex", "asset-index", "--assetsDir", "C:\\Users\\<USER>\\.gradle\\caches\\minecraft\\assets", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-XX:HeapDumpPath=MojangTricksIntelDriversForPerformance_javaw.exe_minecraft.exe.heapdump", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathJunit\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\junit", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}, {"type": "java", "request": "launch", "name": "NeoForge - Server", "presentation": {"group": "NG - NeoForge", "order": 4}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--no<PERSON><PERSON>", "--launchTarget", "neoforgeserverdev", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-Dforge.enabledGameTestNamespaces=collective", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathServer\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\server", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}, {"type": "java", "request": "launch", "name": "NeoForge - ServerData", "presentation": {"group": "NG - NeoForge", "order": 5}, "projectName": "NeoForge", "mainClass": "cpw.mods.bootstraplauncher.BootstrapLauncher", "args": ["--launchTarget", "<PERSON>fo<PERSON><PERSON><PERSON><PERSON><PERSON>", "--fml.fmlVersion", "7.0.1", "--fml.mcV<PERSON>ion", "1.21.5", "--fml.neoForgeVersion", "21.5.2-beta", "--fml.neoFormVersion", "20250325.162830"], "vmArgs": ["-p", "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\bootstraplauncher\\2.0.2\\1a2d076cbc33b0520cbacd591224427b2a20047d\\bootstraplauncher-2.0.2.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\cpw.mods\\securejarhandler\\3.0.8\\c0ef95cecd8699a0449053ac7d9c160748d902cd\\securejarhandler-3.0.8.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-commons\\9.7\\e86dda4696d3c185fcc95d8d311904e7ce38a53f\\asm-commons-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-util\\9.7\\c0655519f24d92af2202cb681cd7c1569df6ead6\\asm-util-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-analysis\\9.7\\e4a258b7eb96107106c0599f0061cfc1832fe07a\\asm-analysis-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm-tree\\9.7\\e446a17b175bfb733b87c5c2560ccb4e57d69f1a\\asm-tree-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\org.ow2.asm\\asm\\9.7\\73d7b3086e14beb604ced229c302feff6449723\\asm-9.7.jar;C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\net.neoforged\\JarJarFileSystems\\0.4.1\\78f59f89defcd032ed788b151ca6a0d40ace796a\\JarJarFileSystems-0.4.1.jar", "--add-modules", "ALL-MODULE-PATH", "--add-opens", "java.base/java.util.jar=cpw.mods.securejarhandler", "--add-opens", "java.base/java.lang.invoke=cpw.mods.securejarhandler", "--add-exports", "java.base/sun.security.util=cpw.mods.securejarhandler", "--add-exports", "jdk.naming.dns/com.sun.jndi.dns=java.naming", "-Dforge.logging.markers=REGISTRIES", "-Dforge.logging.console.level=debug", "-DlegacyClassPath.file=D:\\Collective-1.21.5\\NeoForge\\.gradle\\configuration\\neoForm\\neoFormJoined1.21.5-20250325.162830\\steps\\writeMinecraftClasspathServerData\\classpath.txt", "-DignoreList=mixinextras-neoforge-0.4.1.jar,client-extra,neoforge-", "-Djava.net.preferIPv6Addresses=system"], "cwd": "${workspaceFolder}\\NeoForge\\runs\\serverData", "env": {"MOD_CLASSES": "NeoForge%%D:\\Collective-1.21.5\\NeoForge\\bin\\main"}, "shortenCommandLine": "a<PERSON><PERSON><PERSON>"}]}