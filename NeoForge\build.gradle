plugins {
    id 'java-library'
    id 'eclipse'
    id 'idea'
    id 'net.neoforged.gradle.userdev' version '7.0.171'
//    id 'net.neoforged.gradle.mixin' version '7.+'  // mixin
}

base {
    archivesName = "${mod_name}-neoforge-${minecraft_version}"
}

//mixin { // mixin
//    config 'collective_neoforge.mixins.json' // mixin
//} // mixin

if (file('src/main/resources/META-INF/accesstransformer.cfg').exists()) {
    minecraft.accessTransformers.file file('src/main/resources/META-INF/accesstransformer.cfg')
}

runs {
    configureEach {
        systemProperty 'forge.logging.markers', 'REGISTRIES'
        //systemProperty 'mixin.debug.export', 'true'
        systemProperty 'forge.logging.console.level', 'debug'

        modSource project.sourceSets.main
    }

    client {
       systemProperty 'forge.enabledGameTestNamespaces', project.mod_id
    }

    server {
        systemProperty 'forge.enabledGameTestNamespaces', project.mod_id
        programArgument '--nogui'
    }

    gameTestServer {
        systemProperty 'forge.enabledGameTestNamespaces', project.mod_id
    }
}

sourceSets.main.resources { srcDir 'src/generated/resources' }

dependencies {
    implementation "net.neoforged:neoforge:${neo_version}"
    compileOnly project(":Common")
}

tasks.named('compileJava', JavaCompile).configure {
    source(project(":Common").sourceSets.main.allSource)
}
tasks.withType(Javadoc).configureEach {
    source(project(":Common").sourceSets.main.allJava)
}
tasks.named("sourcesJar", Jar) {
    from(project(":Common").sourceSets.main.allSource)
}

processResources {
    from project(":Common").sourceSets.main.resources
}

tasks.withType(ProcessResources).configureEach {
    var replaceProperties = [
            minecraft_version   : minecraft_version, minecraft_version_range: "",
            neo_version         : neo_version, neo_version_range: "",
            loader_version_range: "",
            mod_id              : mod_id, mod_name: "", mod_license: "", mod_version: "",
            mod_authors         : "", mod_description: "", pack_format_number: "",
    ]
    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml', 'pack.mcmeta']) {
        expand replaceProperties + [project: project]
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}