package com.natamus.collective.functions;

import com.natamus.collective.data.GlobalVariables;
import net.minecraft.ChatFormatting;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.ClickEvent;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraft.network.chat.Style;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.Level;
import net.minecraft.world.phys.AABB;

import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class StringFunctions {
	// START: DO functions
	public static void sendMessage(CommandSourceStack source, String m, ChatFormatting colour) {
		sendMessage(source, m, colour, false);
	}
	public static void sendMessage(Player player, String m, ChatFormatting colour) {
		sendMessage(player, m, colour, false);
	}
	public static void sendMessage(CommandSourceStack source, String m, ChatFormatting colour, boolean emptyline) {
		sendMessage(source, m, colour, emptyline, "");
	}
	public static void sendMessage(Player player, String m, ChatFormatting colour, boolean emptyline) {
		sendMessage(player, m, colour, emptyline, "");
	}
	public static void sendMessage(CommandSourceStack source, String m, ChatFormatting colour, String url) {
		sendMessage(source, m, colour, false, url);
	}
	public static void sendMessage(Player player, String m, ChatFormatting colour, String url) {
		sendMessage(player, m, colour, false, url);
	}

	public static void sendMessage(CommandSourceStack source, String m, ChatFormatting colour, boolean emptyline, String url) {
		if (m.isEmpty()) {
			return;
		}

		if (emptyline) {
			source.sendSuccess(() -> {
				return Component.literal("");
			}, false);
		}

		MutableComponent message = Component.literal(m);
		message.withStyle(colour);
		if (m.contains("http") || !url.isEmpty()) {
			if (url.isEmpty()) {
				for (String word : m.split(" ")) {
					if (word.contains("http")) {
						url = word;
						break;
					}
				}
			}

			if (!url.isEmpty()) {
                try {
                    Style clickstyle = message.getStyle().withClickEvent(new ClickEvent.OpenUrl(new URI(url)));
                    message.withStyle(clickstyle);
                }
                catch (URISyntaxException ignored) { }
			}
		}
		source.sendSuccess(() -> {
			return message;
		}, false);
	}

	public static void sendMessage(Player player, String m, ChatFormatting colour, boolean emptyline, String url) {
		if (player.level().isClientSide) {
			return;
		}

		if (m.isEmpty()) {
			return;
		}

		ServerPlayer serverPlayer = (ServerPlayer)player;
		if (emptyline) {
			serverPlayer.sendSystemMessage(Component.literal(""));
		}

		MutableComponent message = Component.literal(m);
		message.withStyle(colour);
		if (m.contains("http") || !url.isEmpty()) {
			if (url.isEmpty()) {
				for (String word : m.split(" ")) {
					if (word.contains("http")) {
						url = word;
						break;
					}
				}
			}

			if (!url.isEmpty()) {
                try {
                    Style clickstyle = message.getStyle().withClickEvent(new ClickEvent.OpenUrl(new URI(url)));
                    message.withStyle(clickstyle);
                }
                catch (URISyntaxException ignored) { }
			}
		}
		serverPlayer.sendSystemMessage(message);
	}

	public static void broadcastMessage(Level world, String m, ChatFormatting colour) {
		if (m.isEmpty()) {
			return;
		}

		MutableComponent message = Component.literal(m);
		message.withStyle(colour);
		MinecraftServer server = world.getServer();
		if (server == null) {
			return;
		}

		for (Player player : server.getPlayerList().getPlayers()) {
			sendMessage(player, m, colour);
		}
	}

	public static void sendMessageToPlayersAround(Level world, BlockPos p, int radius, String message, ChatFormatting colour) {
		if (message.isEmpty()) {
			return;
		}

		for (Entity around : world.getEntities(null, new AABB(p.getX() - radius, p.getY() - radius, p.getZ() - radius, p.getX() + radius, p.getY() + radius, p.getZ() + radius))) {
			if (around instanceof Player) {
				sendMessage((Player) around, message, colour);
			}
		}
	}

	public static String capitalizeFirst(String string) {
		StringBuilder sb = new StringBuilder(string);
		for(int i=0; i < sb.length(); i++) {
			if(i == 0 || sb.charAt(i-1) == ' ') {
				sb.setCharAt(i, Character.toUpperCase(sb.charAt(i)));
			}
		}
		return sb.toString();
	}

	public static String capitalizeEveryWord(String text) {
		if (text.isEmpty()) {
			return text;
		}

		char[] chars = text.toLowerCase().toCharArray();
		boolean found = false;
		for (int i = 0; i < chars.length; i++) {
			if (!found && Character.isLetter(chars[i])) {
				chars[i] = Character.toUpperCase(chars[i]);
				found = true;
			}
			else if (!(Character.isDigit(chars[i]) || Character.isLetter(chars[i]))) {
				found = false;
			}
		}
		return String.valueOf(chars);
	}

	public static String escapeSpecialRegexChars(String str) {
		return Pattern.compile("[{}()\\[\\].+*?^$\\\\|]").matcher(str).replaceAll("\\\\$0");
	}
	// END: DO functions


	// START: GET functions
	public static String getRandomName(boolean useFemaleNames, boolean useMaleNames) {
		List<String> allnames;
		if (useFemaleNames && useMaleNames) {
			allnames = Stream.concat(GlobalVariables.femaleNames.stream(), GlobalVariables.maleNames.stream()).collect(Collectors.toList());
		}
		else if (useFemaleNames) {
			allnames = GlobalVariables.femaleNames;
		}
		else if (useMaleNames) {
			allnames = GlobalVariables.maleNames;
		}
		else {
			return "";
		}

	    String name = allnames.get(GlobalVariables.random.nextInt(allnames.size())).toLowerCase();
	    return capitalizeEveryWord(name);
	}

	public static String getPCLocalTime(boolean twentyfour, boolean showseconds) {
		String time;
		LocalDateTime now = LocalDateTime.now();
		if (showseconds) {
			if (twentyfour) {
				time = now.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
			}
			else {
				time = now.format(DateTimeFormatter.ofPattern("hh:mm:ss a")).replace("&nbsp;","");
			}
		}
		else {
			if (twentyfour) {
				time = now.format(DateTimeFormatter.ofPattern("HH:mm"));
			}
			else {
				time = now.format(DateTimeFormatter.ofPattern("hh:mm a")).replace("&nbsp;","");
			}
		}
		return time;
	}
	// END: GET functions
	
	// Util
	public static int sequenceCount(String text, String sequence) {
		Pattern pattern = Pattern.compile(sequence);
		Matcher matcher = pattern.matcher(text);

		int count = 0;
		while (matcher.find()) {
		    count++;
		}
	    return count;
	}
	
	public static String joinListWithCommaAnd(List<String> inputlist) {
		if (inputlist.isEmpty()) {
			return "";
		}
		if (inputlist.size() == 1) {
			return inputlist.getFirst();
		}
		
		List<String> list = new ArrayList<>(inputlist);
		String lastelement = list.getLast();
		list.removeLast();
		
		String initial = String.join(", ", list);
		return initial + " and " + lastelement;
	}
}