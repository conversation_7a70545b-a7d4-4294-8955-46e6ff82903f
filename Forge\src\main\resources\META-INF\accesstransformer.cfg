# Forge Access Transformer Entries
public-f net.minecraft.client.Options f_92071_ # gamma
public net.minecraft.client.OptionInstance f_231481_ # value
public net.minecraft.world.entity.animal.MushroomCow m_28928_(Lnet/minecraft/world/entity/animal/MushroomCow$MushroomType;)V # setMushroomType
public net.minecraft.world.entity.monster.Creeper f_32274_ # DATA_IS_POWERED
public net.minecraft.world.level.block.entity.SignBlockEntity m_155724_(Z)[Lnet/minecraft/network/chat/Component; # getMessages
public net.minecraft.world.entity.Entity m_20115_(IZ)V # setSharedFlag
public-f net.minecraft.world.item.trading.MerchantOffer f_45314_ # maxUses
public net.minecraft.world.item.trading.MerchantOffer f_45313_ # uses
public net.minecraft.world.item.trading.MerchantOffer f_45317_ # demand
public net.minecraft.world.level.BaseSpawner f_45442_ # spawnDelay
public net.minecraft.world.entity.LivingEntity f_20961_ # DATA_HEALTH_ID
public net.minecraft.world.entity.projectile.FishingHook f_37099_ # biting
public net.minecraft.world.entity.Entity f_19815_ # dimensions
public net.minecraft.world.entity.Entity f_19816_ # eyeHeight
public net.minecraft.client.KeyMapping f_90816_ # key
public net.minecraft.world.inventory.MerchantMenu f_40027_ # trader
public net.minecraft.client.gui.screens.worldselection.CreateWorldScreen m_142416_(Lnet/minecraft/client/gui/components/events/GuiEventListener;)Lnet/minecraft/client/gui/components/events/GuiEventListener; # addRenderableWidget
public net.minecraft.client.gui.screens.Screen m_142416_(Lnet/minecraft/client/gui/components/events/GuiEventListener;)Lnet/minecraft/client/gui/components/events/GuiEventListener; # addRenderableWidget
public-f net.minecraft.client.gui.screens.Screen f_96539_ # title
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_244140_ # sign
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_276619_ # text
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_244359_ # messages
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_244562_ # line
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_243993_ # signField
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen f_276451_ # isFrontText
public net.minecraft.world.entity.animal.horse.AbstractHorse m_30647_(I)Z # getFlag
public net.minecraft.world.level.block.TrapDoorBlock m_306287_()Lnet/minecraft/world/level/block/state/properties/BlockSetType; # getType
public net.minecraft.world.level.ServerExplosion m_356540_()Ljava/util/List; # calculateExplodedPositions
public-f net.minecraft.world.level.ServerExplosion f_349523_ # radius
public net.minecraft.world.item.context.UseOnContext <init>(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/BlockHitResult;)V # UseOnContext
public net.minecraft.world.entity.player.Inventory f_381534_ # equipment
public net.minecraft.world.entity.animal.MushroomCow m_28928_(Lnet/minecraft/world/entity/animal/MushroomCow$Variant;)V # setVariant
public net.minecraft.world.entity.monster.Shulker m_262499_(Ljava/util/Optional;)V # setVariant
public net.minecraft.world.entity.decoration.Painting m_218891_(Lnet/minecraft/core/Holder;)V # setVariant
public net.minecraft.world.entity.animal.Rabbit m_262421_(Lnet/minecraft/world/entity/animal/Rabbit$Variant;)V # setVariant