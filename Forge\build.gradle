plugins {
    id 'idea'
    id 'maven-publish'
    id 'net.minecraftforge.gradle' version '[6.0.24,6.2)'
    id 'org.spongepowered.mixin' version "0.7-SNAPSHOT" // mixin
}

base {
    archivesName = "${mod_name}-forge-${minecraft_version}"
}

mixin { // mixin
    config 'collective_forge.mixins.json' // mixin
    
    debug.verbose = true // mixin
    debug.export = true // mixin
} // mixin

minecraft {
    mappings channel: 'official', version: minecraft_version
    // mappings channel: "parchment", version: "1.20.3-2023.12.31-${minecraft_version}"

    reobf = false

    copyIdeResources = true //Calls processResources when in dev

    // Automatically enable forge AccessTransformers if the file exists
    // This location is hardcoded in Forge and can not be changed.
    // https://github.com/MinecraftForge/MinecraftForge/blob/be1698bb1554f9c8fa2f58e32b9ab70bc4385e60/fmlloader/src/main/java/net/minecraftforge/fml/loading/moddiscovery/ModFile.java#L123
    if (file('src/main/resources/META-INF/accesstransformer.cfg').exists()) {
        accessTransformer = file('src/main/resources/META-INF/accesstransformer.cfg')
    }

    runs {
        client {
            workingDirectory project.file('run')
            args "-mixin.config=collective_forge.mixins.json" // mixin
            
            ideaModule "${rootProject.name}.${project.name}.main"
            taskName 'Client'
            mods {
                modClientRun {
                    source sourceSets.main
                    source project(":Common").sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')
            args "-mixin.config=collective_forge.mixins.json" // mixin

            ideaModule "${rootProject.name}.${project.name}.main"
            taskName 'Server'
            mods {
                modServerRun {
                    source sourceSets.main
                    source project(":Common").sourceSets.main
                }
            }
        }

        data {
            workingDirectory project.file('run')
            args "-mixin.config=collective_forge.mixins.json" // mixin

            ideaModule "${rootProject.name}.${project.name}.main"
            args '--mod', mod_id, '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
            taskName 'Data'
            mods {
                modDataRun {
                    source sourceSets.main
                    source project(":Common").sourceSets.main
                }
            }
        }
    }
}

//sourceSets.main.resources.srcDir 'src/generated/resources'
sourceSets.main.resources { srcDir 'src/generated/resources' }

dependencies {
    compileOnly group:'org.spongepowered', name:'mixin', version:'0.8.5'

    minecraft "net.minecraftforge:forge:${minecraft_version}-${forge_version}"

    implementation('net.sf.jopt-simple:jopt-simple:5.0.4') { version { strictly '5.0.4' } }

    compileOnly project(":Common")
}

tasks.named('compileJava', JavaCompile).configure {
    source(project(":Common").sourceSets.main.allSource)
}
tasks.withType(Javadoc).configureEach {
    source(project(":Common").sourceSets.main.allJava)
}
tasks.named("sourcesJar", Jar) {
    from(project(":Common").sourceSets.main.allSource)
}

sourceSets.each {
    def dir = layout.buildDirectory.dir("sourcesSets/$it.name")
    it.output.resourcesDir = dir
    it.java.destinationDirectory = dir
}

processResources {
    from project(":Common").sourceSets.main.resources
}

jar {
    manifest {
        attributes([
            "MixinConfigs" : "collective_forge.mixins.json" // mixin
        ])
    }
}