package com.natamus.collective.fabric.mixin;

import net.minecraft.client.Camera;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.level.BlockGetter;
import net.minecraft.world.phys.Vec3;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(value = Camera.class, priority = 1001)
public class CameraMixin {
    
    @Shadow
    private Vec3 position;
    
    @Shadow
    private float xRot;
    
    @Shadow
    private float yRot;
    
    @Inject(method = "setup", at = @At("TAIL"))
    private void onSetup(BlockGetter blockGetter, Entity entity, boolean bl, boolean bl2, float f, CallbackInfo ci) {
        try {
            Class<?> systemEnhancementClass = Class.forName("com.natamus.collective.features.SystemEnhancement");
            boolean freecamActive = (Boolean) systemEnhancementClass.getMethod("isFreecamActive").invoke(null);
            
            if (freecamActive) {
                // Override camera position with freecam position
                double freecamX = (Double) systemEnhancementClass.getMethod("getFreecamX").invoke(null);
                double freecamY = (Double) systemEnhancementClass.getMethod("getFreecamY").invoke(null);
                double freecamZ = (Double) systemEnhancementClass.getMethod("getFreecamZ").invoke(null);
                float freecamYaw = (Float) systemEnhancementClass.getMethod("getFreecamYaw").invoke(null);
                float freecamPitch = (Float) systemEnhancementClass.getMethod("getFreecamPitch").invoke(null);
                
                this.position = new Vec3(freecamX, freecamY, freecamZ);
                this.yRot = freecamYaw;
                this.xRot = freecamPitch;
            }
        } catch (Exception ignored) {
            // Enhancement features not available
        }
    }
}
