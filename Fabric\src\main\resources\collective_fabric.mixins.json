{"required": true, "minVersion": "0.8", "package": "com.natamus.collective.fabric.mixin", "plugin": "com.natamus.collective.fabric.mixin.plugin.FabricMixinConfigPlugin", "compatibilityLevel": "JAVA_21", "mixins": ["AbstractFurnaceBlockEntityMixin", "AbstractHorseMixin", "AnimalMixin", "AnvilMenuMixin", "BaseSpawnerMixin", "BlockEntityMixin", "BlockItemMixin", "BlockMixin", "BoneMealItemMixin", "BowItemMixin", "CommandsMixin", "EntityTypeMixin", "FishingRodHookedTriggerMixin", "ItemEntityMixin", "ItemStackMixin", "LanguageMixin", "LightningBoltMixin", "LivingEntityMixin", "MagmaCubeMixin", "MinecraftServerMixin", "NaturalSpawnerMixin", "PistonBaseBlockMixin", "PlayerListMixin", "PlayerMixin", "PortalShapeMixin", "ServerExplosionMixin", "ServerGamePacketListenerImplMixin", "ServerLevelMixin", "ServerPlayerGameModeMixin", "ServerPlayerMixin", "TeleportCommandMixin"], "client": ["CameraMixin", "ChatListenerMixin", "ClientLevelMixin", "ClientPacketListenerMixin", "ClientPlayerMixin", "CreateWorldScreenMixin", "GameRendererMixin", "ItemInHandRendererMixin", "LevelRendererMixin", "MinecraftMixin", "MultiPlayerGameModeMixin", "SoundEngineMixin", "TitleScreenMixin", "WorldOpenFlowsMixin"], "server": [], "injectors": {"defaultRequire": 1}}