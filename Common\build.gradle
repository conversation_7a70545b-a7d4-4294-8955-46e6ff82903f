plugins {
    id 'idea'
    id 'java'
    id 'org.spongepowered.gradle.vanilla' version '0.2.1-SNAPSHOT'
}

base {
    archivesName = "${mod_name}-common-${minecraft_version}"
}

minecraft {
    version(minecraft_version)
    if(file("src/main/resources/${mod_id}.accesswidener").exists()){
        accessWideners(file("src/main/resources/${mod_id}.accesswidener"))
    }
}

dependencies {
    compileOnly group:'org.spongepowered', name:'mixin', version:'0.8.5'
    implementation group: 'com.google.code.findbugs', name: 'jsr305', version: '3.0.1'
}