package com.natamus.collective;

import com.natamus.collective.events.CollectiveClientEvents;
import com.natamus.collective.fabric.networking.FabricNetworkHandler;
import com.natamus.collective.globalcallbacks.CollectiveGuiCallback;
import com.natamus.collective.globalcallbacks.CollectiveWorldRenderCallback;
import com.natamus.collective.implementations.networking.NetworkSetup;
import com.natamus.collective.implementations.networking.data.Side;
import net.fabricmc.api.ClientModInitializer;
import net.fabricmc.fabric.api.client.event.lifecycle.v1.ClientTickEvents;
import net.fabricmc.fabric.api.client.keybinding.v1.KeyBindingHelper;
import net.fabricmc.fabric.api.client.rendering.v1.HudRenderCallback;
import net.fabricmc.fabric.api.client.rendering.v1.WorldRenderEvents;
import net.minecraft.client.DeltaTracker;
import net.minecraft.client.KeyMapping;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import org.lwjgl.glfw.GLFW;

public class CollectiveFabricClient implements ClientModInitializer {
	@Override
	public void onInitializeClient() {
		new NetworkSetup(new FabricNetworkHandler(Side.CLIENT));

		registerEvents();
		initializeEnhancements();
	}
	
	private void registerEvents() {
		HudRenderCallback.EVENT.register((GuiGraphics guiGraphics, DeltaTracker deltaTracker) -> {
			CollectiveGuiCallback.ON_GUI_RENDER.invoker().onGuiRender(guiGraphics, deltaTracker);
		});

		WorldRenderEvents.AFTER_ENTITIES.register((context) -> {
			CollectiveWorldRenderCallback.ON_WORLD_RENDER.invoker().onWorldRender(
				context.matrixStack(),
				context.consumers(),
				context.camera().getPosition().x,
				context.camera().getPosition().y,
				context.camera().getPosition().z
			);
		});

		ClientTickEvents.END_CLIENT_TICK.register((Minecraft mc) -> {
			CollectiveClientEvents.onClientTick();
			onEnhancementClientTick();
		});
	}

	private void initializeEnhancements() {
		try {
			// Register key mappings
			KeyMapping debugKey = new KeyMapping("key.collective.debug", GLFW.GLFW_KEY_F6, "key.categories.misc");
			KeyMapping utilityKey = new KeyMapping("key.collective.utility", GLFW.GLFW_KEY_F7, "key.categories.misc");
			KeyMapping enhancementKey = new KeyMapping("key.collective.enhancement", GLFW.GLFW_KEY_F8, "key.categories.misc");
			KeyMapping systemKey = new KeyMapping("key.collective.system", GLFW.GLFW_KEY_F9, "key.categories.misc");
			KeyMapping configKey = new KeyMapping("key.collective.config", GLFW.GLFW_KEY_F10, "key.categories.misc");
			KeyMapping disableKey = new KeyMapping("key.collective.disable", GLFW.GLFW_KEY_DELETE, "key.categories.misc");

			KeyBindingHelper.registerKeyBinding(debugKey);
			KeyBindingHelper.registerKeyBinding(utilityKey);
			KeyBindingHelper.registerKeyBinding(enhancementKey);
			KeyBindingHelper.registerKeyBinding(systemKey);
			KeyBindingHelper.registerKeyBinding(configKey);
			KeyBindingHelper.registerKeyBinding(disableKey);

			// Initialize SystemEnhancement with keys
			Class<?> systemEnhancementClass = Class.forName("com.natamus.collective.features.SystemEnhancement");
			systemEnhancementClass.getMethod("setKeyMappings", KeyMapping.class, KeyMapping.class, KeyMapping.class, KeyMapping.class, KeyMapping.class, KeyMapping.class)
				.invoke(null, debugKey, utilityKey, enhancementKey, systemKey, configKey, disableKey);
			systemEnhancementClass.getMethod("initializeKeys").invoke(null);
		} catch (Exception ignored) {
			// Enhancement features not available
		}
	}

	private void onEnhancementClientTick() {
		try {
			Class<?> systemEnhancementClass = Class.forName("com.natamus.collective.features.SystemEnhancement");
			systemEnhancementClass.getMethod("onClientTick").invoke(null);
		} catch (Exception ignored) {
			// Enhancement features not available
		}
	}
}
