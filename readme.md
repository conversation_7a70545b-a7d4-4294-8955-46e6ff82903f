<h2>Collective</h2>
<p><a href="https://github.com/Serilum/Collective"><img src="https://serilum.com/assets/data/logo/collective.png"></a></p><h2>Download</h2>
<p>You can download Collective on CurseForge and Modrinth:</p><p>&nbsp;&nbsp;CurseForge: &nbsp;&nbsp;<a href="https://curseforge.com/minecraft/mc-mods/collective">https://curseforge.com/minecraft/mc-mods/collective</a><br>&nbsp;&nbsp;Modrinth: &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;<a href="https://modrinth.com/mod/collective">https://modrinth.com/mod/collective</a></p>
<h2>Issue Tracker</h2>
<p>To keep a better overview of all mods, the issue tracker is located in a separate repository.<br>&nbsp;&nbsp;For issues, ideas, suggestions or anything else, please follow this link:</p>
<p>&nbsp;&nbsp;&nbsp;&nbsp;-> <a href="https://serilum.com/url/issue-tracker">Issue Tracker</a></p>
<h2>Pull Requests</h2>
<p>Because of the way mod loader files are bundled into one jar, some extra information is needed to do a PR.<br>&nbsp;&nbsp;A wiki page entry about it is available here:</p>
<p>&nbsp;&nbsp;&nbsp;&nbsp;-> <a href="https://serilum.com/url/pull-requests">Pull Request Information</a></p>
<h2>Mod Description</h2>
<p style="text-align:center"><a href="https://serilum.com/" rel="nofollow"><img src="https://github.com/Serilum/.cdn/raw/main/description/header/header.png" alt="" width="838" height="400"></a></p>
<p style="text-align:center"><a href="https://curseforge.com/members/serilum/projects" target="_blank" rel="nofollow"><img src="https://raw.githubusercontent.com/Serilum/.data-workflow/main/badges/svg/curseforge.svg" width="200"></a> <a href="https://modrinth.com/user/Serilum" target="_blank" rel="nofollow"><img src="https://raw.githubusercontent.com/Serilum/.data-workflow/main/badges/svg/modrinth.svg" width="200"></a> <a href="https://patreon.com/serilum" target="_blank" rel="nofollow"><img src="https://raw.githubusercontent.com/Serilum/.data-workflow/main/badges/svg/patreon.svg" width="200"></a> <a href="https://youtube.com/@serilum" target="_blank" rel="nofollow"><img src="https://raw.githubusercontent.com/Serilum/.data-workflow/main/badges/svg/youtube.svg" width="200"></a></p>
<p><br><span style="font-size:24px">Collective is a shared library mod with common code for all of Serilum's mods. </span></p>
<p style="font-size:16px">It contains data and functions centralized in one place. Collective helps a great deal in maintaining all the Fabric, Forge and NeoForge mod loader versions.</p>
<p style="font-size:16px">Having access to the library's code environment, creates possibilities for current and future project features. The updating process is also made easier when there's an improvement for a function that is relevant to many others. For example this means having to update one project, instead of six. This saves time and makes me a little happier.</p>
<p>&nbsp;<span style="font-size:20px;font-weight:bolder;color:#000"><br><span style="font-size:24px">Features:</span></span></p>
<ul>
<li><span style="font-size:18px;color:#000">Manages all config files and in-game menu of dependent mods. <span style="font-size:24px"><strong>&sup1;</strong></span><br></span></li>
<li><span style="font-size:18px;color:#000">Variables for increased compatibility with other projects.<br></span></li>
<li><span style="font-size:18px;color:#000">Lots of useful functions to prevent code duplication. <span style="font-size:24px"><strong>&sup2;</strong></span><br></span></li>
<li><span style="font-size:18px;color:#000">An event to replace and resupply entities.<br></span></li>
<li><span style="font-size:18px;color:#000">Allows toggling the functionality of bundled mods.&nbsp;<span style="font-size:24px"><strong>&sup3;</strong></span><br></span></li>
<li><span style="font-size:18px;color:#000">Contains the access transformers/wideners to use private fields.&nbsp;<span style="font-size:24px"><strong>⁴</strong></span><br></span></li>
<li><span style="font-size:18px;color:#000">Networking code to send packets via the Common source set.<br></span></li>
<li><span style="font-size:18px;color:#000">Centralized backwards compatibility for major version changes.<br></span></li>
<li><span style="font-size:18px;color:#000">And much more!&nbsp;<span style="font-size:24px"><strong>⁵</strong></span></span></li>
</ul>
<p><strong><br><br><span style="font-size:24px">Extra Information:</span><br></strong></p>
<p><span style="font-size:18px"><strong>1</strong> | Because I use the Multiloader template for all of my mods, there is no built-in config I can use from within the Common code. Collective contains a suite of config code that all dependent mods use. This adds an in-game config menu and generates files in '<em>.minecraft/config/</em>'.<br><span style="font-size:18px"><span style="color:#008000"><a style="font-size:18px;color:#008000" href="https://github.com/Serilum/Collective/tree/HEAD/Common/src/main/java/com/natamus/collective/config" target="_blank" rel="nofollow">&lt; Common's <em>Config Code</em> &gt;</a></span></span></span><br><picture><img src="https://github.com/Serilum/.cdn/raw/main/projects/collective/a.png"></picture><br><br><picture><img src="https://github.com/Serilum/.cdn/raw/main/projects/collective/b.png"></picture><br><br><br><span style="font-size:18px"><strong>2</strong> | Over the years, I've added lots of useful code to Collective. Being able to use these makes my life of updating dependent mods a lot easier:<br><span style="font-size:18px"><span style="color:#008000"><a style="font-size:18px;color:#008000" href="https://github.com/Serilum/Collective/tree/HEAD/Common/src/main/java/com/natamus/collective/functions" target="_blank" rel="nofollow">&lt; Common's <em>Functions</em> &gt;</a></span></span></span><br><picture><img src="https://github.com/Serilum/.cdn/raw/main/projects/collective/c.png"></picture><br><br><br><span style="font-size:18px"><strong>3</strong> | I've released bundles of my mods for easier downloading!</span><br><span style="font-size:18px">Collective allows enabling/disabling the included mods via some neat code!<br><span style="font-size:18px"><span style="color:#008000"><a style="font-size:18px;color:#008000" href="https://github.com/Serilum/Collective/tree/HEAD/Fabric/src/main/java/com/natamus/collective/fabric/bundle" target="_blank" rel="nofollow">&lt; Fabric's <em>Bundle Code</em> &gt;</a></span></span></span><br><picture><img src="https://github.com/Serilum/.cdn/raw/main/projects/collective/d.png"></picture><br><br><br><span style="font-size:18px"><strong>4</strong> | Access transformers/wideners are used to set and use fields Mojang has made private.&nbsp;</span><span style="font-size:18px">Having these in Collective keeps everything organised:<br><span style="font-size:18px"><span style="color:#008000"><a style="font-size:18px;color:#008000" href="https://github.com/Serilum/Collective/blob/HEAD/Common/src/main/resources/collective.accesswidener" target="_blank" rel="nofollow">&lt; Fabric's <em>collective.accesswidener</em> &gt;</a></span></span></span><br><picture><img src="https://github.com/Serilum/.cdn/raw/main/projects/collective/e.png"></picture><br><br><br><span style="font-size:18px"><strong>5</strong> | See the full source code here:</span><br><span style="font-size:24px;color:#008000"><a style="font-size:24px;color:#008000" href="https://github.com/Serilum/Collective" target="_blank" rel="nofollow"><strong>https://github.com/Serilum/Collective</strong></a></span><br><br><br>------------------<br><br><span style="font-size:24px"><strong>You may freely use this mod in any modpack, as long as the download remains hosted within the CurseForge or Modrinth ecosystem.</strong></span><br><br><span style="font-size:18px"><a style="font-size:18px;color:#008000" href="https://serilum.com/" rel="nofollow">Serilum.com</a> contains an overview and more information on all mods available.</span><br><br><span style="font-size:14px">Comments are disabled as I'm unable to keep track of all the separate pages on each mod.</span><span style="font-size:14px"><br>For issues, ideas, suggestions or anything else there is the&nbsp;<a style="font-size:14px;color:#008000" href="https://github.com/Serilum/.issue-tracker" rel="nofollow">Github repo</a>. Thanks!</span><span style="font-size:6px"><br><br></span></p>
<p style="text-align:center"><a href="https://serilum.com/donate" rel="nofollow"><img src="https://github.com/Serilum/.cdn/raw/main/description/projects/support.svg" alt="" width="306" height="50"></a></p>