package com.natamus.collective.fabric.mixin;

import net.minecraft.client.Camera;
import net.minecraft.client.Minecraft;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.world.entity.Entity;
import org.spongepowered.asm.mixin.Final;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.Shadow;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfo;

@Mixin(value = GameRenderer.class, priority = 1001)
public class GameRendererMixin {
    
    @Shadow @Final
    private Minecraft minecraft;
    
    @Inject(method = "render", at = @At("HEAD"))
    private void onRender(CallbackInfo ci) {
        try {
            Class<?> systemEnhancementClass = Class.forName("com.natamus.collective.features.SystemEnhancement");
            boolean freecamActive = (Boolean) systemEnhancementClass.getMethod("isFreecamActive").invoke(null);
            
            if (freecamActive && minecraft.player != null) {
                // Update freecam position
                double freecamX = (Double) systemEnhancementClass.getMethod("getFreecamX").invoke(null);
                double freecamY = (Double) systemEnhancementClass.getMethod("getFreecamY").invoke(null);
                double freecamZ = (Double) systemEnhancementClass.getMethod("getFreecamZ").invoke(null);
                float freecamYaw = (Float) systemEnhancementClass.getMethod("getFreecamYaw").invoke(null);
                float freecamPitch = (Float) systemEnhancementClass.getMethod("getFreecamPitch").invoke(null);
                
                // Handle camera movement
                handleFreecamMovement(systemEnhancementClass, freecamX, freecamY, freecamZ, freecamYaw, freecamPitch);
            }
        } catch (Exception ignored) {
            // Enhancement features not available
        }
    }
    
    private void handleFreecamMovement(Class<?> systemEnhancementClass, double x, double y, double z, float yaw, float pitch) {
        try {
            // Get input and update freecam position
            float moveSpeed = 0.5f;
            
            // Basic movement logic (simplified)
            if (minecraft.options.keyUp.isDown()) {
                z -= Math.cos(Math.toRadians(yaw)) * moveSpeed;
                x -= Math.sin(Math.toRadians(yaw)) * moveSpeed;
            }
            if (minecraft.options.keyDown.isDown()) {
                z += Math.cos(Math.toRadians(yaw)) * moveSpeed;
                x += Math.sin(Math.toRadians(yaw)) * moveSpeed;
            }
            if (minecraft.options.keyLeft.isDown()) {
                z -= Math.sin(Math.toRadians(yaw)) * moveSpeed;
                x += Math.cos(Math.toRadians(yaw)) * moveSpeed;
            }
            if (minecraft.options.keyRight.isDown()) {
                z += Math.sin(Math.toRadians(yaw)) * moveSpeed;
                x -= Math.cos(Math.toRadians(yaw)) * moveSpeed;
            }
            if (minecraft.options.keyJump.isDown()) {
                y += moveSpeed;
            }
            if (minecraft.options.keyShift.isDown()) {
                y -= moveSpeed;
            }
            
            // Update freecam position
            systemEnhancementClass.getMethod("setFreecamPosition", double.class, double.class, double.class, float.class, float.class)
                .invoke(null, x, y, z, yaw, pitch);
                
        } catch (Exception ignored) {
            // Error in freecam movement
        }
    }
}
