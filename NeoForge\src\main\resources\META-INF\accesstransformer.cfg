# NeoForge Access Transformer Entries
public-f net.minecraft.client.Options gamma # gamma
public net.minecraft.client.OptionInstance value # value
public net.minecraft.world.entity.animal.MushroomCow setMushroomType(Lnet.minecraft/world/entity/animal/MushroomCow$MushroomType;)V # setMushroomType
public net.minecraft.world.entity.monster.Creeper DATA_IS_POWERED # DATA_IS_POWERED
public net.minecraft.world.level.block.entity.SignBlockEntity getMessages(Z)[Lnet/minecraft/network/chat/Component; # getMessages
public net.minecraft.world.entity.Entity setSharedFlag(IZ)V # setSharedFlag
public-f net.minecraft.world.item.trading.MerchantOffer maxUses # maxUses
public net.minecraft.world.item.trading.MerchantOffer uses # uses
public net.minecraft.world.item.trading.MerchantOffer demand # demand
public net.minecraft.world.level.BaseSpawner spawnDelay # spawnDelay
public net.minecraft.world.entity.LivingEntity DATA_HEALTH_ID # DATA_HEALTH_ID
public net.minecraft.world.entity.projectile.FishingHook biting # biting
public net.minecraft.world.entity.Entity dimensions # dimensions
public net.minecraft.world.entity.Entity eyeHeight # eyeHeight
public net.minecraft.client.KeyMapping key # key
public net.minecraft.world.inventory.MerchantMenu trader # trader
public net.minecraft.client.gui.screens.worldselection.CreateWorldScreen addRenderableWidget(Lnet/minecraft/client/gui/components/events/GuiEventListener;)Lnet/minecraft/client/gui/components/events/GuiEventListener; # addRenderableWidget
public net.minecraft.client.gui.screens.Screen addRenderableWidget(Lnet/minecraft/client/gui/components/events/GuiEventListener;)Lnet/minecraft/client/gui/components/events/GuiEventListener; # addRenderableWidget
public-f net.minecraft.client.gui.screens.Screen title # title
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen sign # sign
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen text # text
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen messages # messages
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen line # line
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen signField # signField
public net.minecraft.client.gui.screens.inventory.AbstractSignEditScreen isFrontText # isFrontText
public net.minecraft.world.entity.animal.horse.AbstractHorse getFlag(I)Z # getFlag
public net.minecraft.world.level.block.TrapDoorBlock getType()Lnet/minecraft/world/level/block/state/properties/BlockSetType; # getType
public net.minecraft.world.level.ServerExplosion calculateExplodedPositions()Ljava/util/List; # calculateExplodedPositions
public-f net.minecraft.world.level.ServerExplosion radius # radius
public net.minecraft.world.item.context.UseOnContext <init>(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/BlockHitResult;)V # UseOnContext
public net.minecraft.world.entity.player.Inventory equipment # equipment
public net.minecraft.world.entity.animal.MushroomCow setVariant(Lnet/minecraft/world/entity/animal/MushroomCow$Variant;)V # setVariant
public net.minecraft.world.entity.monster.Shulker setVariant(Ljava/util/Optional;)V # setVariant
public net.minecraft.world.entity.decoration.Painting setVariant(Lnet/minecraft/core/Holder;)V # setVariant
public net.minecraft.world.entity.animal.Rabbit setVariant(Lnet/minecraft/world/entity/animal/Rabbit$Variant;)V # setVariant