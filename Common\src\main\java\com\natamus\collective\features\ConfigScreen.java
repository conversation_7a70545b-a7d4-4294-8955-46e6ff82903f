package com.natamus.collective.features;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ConfigScreen extends Screen {
    private final Screen parent;
    private EditBox searchBox;
    private EditBox blockNameBox;
    private Button addBlockButton;
    private Button removeBlockButton;
    private Button toggleFreecamDamageButton;
    private List<String> blockList;
    private int selectedBlockIndex = -1;
    private int scrollOffset = 0;
    
    public ConfigScreen(Screen parent) {
        super(Component.literal("System Configuration"));
        this.parent = parent;
        this.blockList = new ArrayList<>();
        updateBlockList();
    }
    
    @Override
    protected void init() {
        // Search box
        this.searchBox = new EditBox(this.font, this.width / 2 - 100, 40, 200, 20, Component.literal("Search"));
        this.searchBox.setHint(Component.literal("Search functions..."));
        this.addRenderableWidget(this.searchBox);
        
        // Block name input
        this.blockNameBox = new EditBox(this.font, this.width / 2 - 100, 80, 150, 20, Component.literal("Block Name"));
        this.blockNameBox.setHint(Component.literal("Enter block name..."));
        this.addRenderableWidget(this.blockNameBox);
        
        // Add block button
        this.addBlockButton = Button.builder(Component.literal("Add"), (button) -> {
            String blockName = this.blockNameBox.getValue().trim();
            if (!blockName.isEmpty() && !SystemEnhancement.getBlockEspList().containsKey(blockName)) {
                SystemEnhancement.getBlockEspList().put(blockName, true);
                this.blockNameBox.setValue("");
                updateBlockList();
            }
        }).pos(this.width / 2 + 60, 80).size(40, 20).build();
        this.addRenderableWidget(this.addBlockButton);
        
        // Remove block button
        this.removeBlockButton = Button.builder(Component.literal("Remove"), (button) -> {
            if (selectedBlockIndex >= 0 && selectedBlockIndex < blockList.size()) {
                String blockName = blockList.get(selectedBlockIndex);
                SystemEnhancement.getBlockEspList().remove(blockName);
                selectedBlockIndex = -1;
                updateBlockList();
            }
        }).pos(this.width / 2 - 100, this.height - 60).size(60, 20).build();
        this.addRenderableWidget(this.removeBlockButton);
        
        // Toggle freecam damage button
        this.toggleFreecamDamageButton = Button.builder(
            Component.literal("Freecam Damage: " + (SystemEnhancement.isFreecamDamageDisable() ? "Disable" : "Keep")), 
            (button) -> {
                SystemEnhancement.setFreecamDamageDisable(!SystemEnhancement.isFreecamDamageDisable());
                button.setMessage(Component.literal("Freecam Damage: " + (SystemEnhancement.isFreecamDamageDisable() ? "Disable" : "Keep")));
            }
        ).pos(this.width / 2 - 30, this.height - 60).size(120, 20).build();
        this.addRenderableWidget(this.toggleFreecamDamageButton);
        
        // Done button
        Button doneButton = Button.builder(Component.literal("Done"), (button) -> {
            this.minecraft.setScreen(this.parent);
        }).pos(this.width / 2 + 100, this.height - 60).size(60, 20).build();
        this.addRenderableWidget(doneButton);
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);
        
        // Title
        guiGraphics.drawCenteredString(this.font, this.title, this.width / 2, 20, 0xFFFFFF);
        
        // Instructions
        guiGraphics.drawString(this.font, "Block ESP Configuration:", this.width / 2 - 100, 110, 0xAAAAAA);
        
        // Block list
        renderBlockList(guiGraphics, mouseX, mouseY);
        
        // Feature status
        renderFeatureStatus(guiGraphics);
        
        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }
    
    private void renderBlockList(GuiGraphics guiGraphics, int mouseX, int mouseY) {
        int listX = this.width / 2 - 100;
        int listY = 130;
        int listWidth = 200;
        int listHeight = this.height - 220;
        int itemHeight = 15;
        
        // Background
        guiGraphics.fill(listX, listY, listX + listWidth, listY + listHeight, 0x80000000);
        
        // Items
        int visibleItems = listHeight / itemHeight;
        int maxScroll = Math.max(0, blockList.size() - visibleItems);
        scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
        
        for (int i = 0; i < Math.min(visibleItems, blockList.size() - scrollOffset); i++) {
            int index = i + scrollOffset;
            String blockName = blockList.get(index);
            int itemY = listY + i * itemHeight;
            
            // Selection highlight
            if (index == selectedBlockIndex) {
                guiGraphics.fill(listX, itemY, listX + listWidth, itemY + itemHeight, 0x80FFFFFF);
            }
            
            // Hover highlight
            if (mouseX >= listX && mouseX < listX + listWidth && mouseY >= itemY && mouseY < itemY + itemHeight) {
                guiGraphics.fill(listX, itemY, listX + listWidth, itemY + itemHeight, 0x40FFFFFF);
            }
            
            // Text
            guiGraphics.drawString(this.font, blockName, listX + 5, itemY + 3, 0xFFFFFF);
        }
    }
    
    private void renderFeatureStatus(GuiGraphics guiGraphics) {
        int statusX = this.width / 2 + 120;
        int statusY = 130;
        
        guiGraphics.drawString(this.font, "Feature Status:", statusX, statusY, 0xAAAAAA);
        
        int y = statusY + 20;
        guiGraphics.drawString(this.font, "Freecam: " + (SystemEnhancement.isFreecamActive() ? "ON" : "OFF"), statusX, y, 
            SystemEnhancement.isFreecamActive() ? 0x00FF00 : 0xFF0000);
        
        y += 15;
        guiGraphics.drawString(this.font, "Player Highlight: " + (SystemEnhancement.isPlayerHighlightActive() ? "ON" : "OFF"), statusX, y,
            SystemEnhancement.isPlayerHighlightActive() ? 0x00FF00 : 0xFF0000);
        
        y += 15;
        guiGraphics.drawString(this.font, "Target HUD: " + (SystemEnhancement.isTargetHudActive() ? "ON" : "OFF"), statusX, y,
            SystemEnhancement.isTargetHudActive() ? 0x00FF00 : 0xFF0000);
        
        y += 15;
        guiGraphics.drawString(this.font, "Entity ESP: " + (SystemEnhancement.isEntityEspActive() ? "ON" : "OFF"), statusX, y,
            SystemEnhancement.isEntityEspActive() ? 0x00FF00 : 0xFF0000);
        
        y += 15;
        guiGraphics.drawString(this.font, "Block ESP: " + (SystemEnhancement.isBlockEspActive() ? "ON" : "OFF"), statusX, y,
            SystemEnhancement.isBlockEspActive() ? 0x00FF00 : 0xFF0000);
        
        y += 30;
        guiGraphics.drawString(this.font, "Keybinds:", statusX, y, 0xAAAAAA);
        y += 15;
        guiGraphics.drawString(this.font, "F6 - Toggle Freecam", statusX, y, 0xCCCCCC);
        y += 12;
        guiGraphics.drawString(this.font, "F7 - Player Highlight", statusX, y, 0xCCCCCC);
        y += 12;
        guiGraphics.drawString(this.font, "F8 - Target HUD", statusX, y, 0xCCCCCC);
        y += 12;
        guiGraphics.drawString(this.font, "F9 - Entity ESP", statusX, y, 0xCCCCCC);
        y += 12;
        guiGraphics.drawString(this.font, "F10 - Config", statusX, y, 0xCCCCCC);
        y += 12;
        guiGraphics.drawString(this.font, "DEL - Disable Mod", statusX, y, 0xFF0000);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle block list clicks
        int listX = this.width / 2 - 100;
        int listY = 130;
        int listWidth = 200;
        int listHeight = this.height - 220;
        int itemHeight = 15;
        
        if (mouseX >= listX && mouseX < listX + listWidth && mouseY >= listY && mouseY < listY + listHeight) {
            int clickedIndex = (int) ((mouseY - listY) / itemHeight) + scrollOffset;
            if (clickedIndex >= 0 && clickedIndex < blockList.size()) {
                selectedBlockIndex = clickedIndex;
                return true;
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseScrolled(double mouseX, double mouseY, double scrollX, double scrollY) {
        // Handle scrolling in block list
        int listX = this.width / 2 - 100;
        int listY = 130;
        int listWidth = 200;
        int listHeight = this.height - 220;
        
        if (mouseX >= listX && mouseX < listX + listWidth && mouseY >= listY && mouseY < listY + listHeight) {
            scrollOffset -= (int) scrollY;
            int maxScroll = Math.max(0, blockList.size() - (listHeight / 15));
            scrollOffset = Math.max(0, Math.min(scrollOffset, maxScroll));
            return true;
        }
        
        return super.mouseScrolled(mouseX, mouseY, scrollX, scrollY);
    }
    
    private void updateBlockList() {
        blockList.clear();
        for (Map.Entry<String, Boolean> entry : SystemEnhancement.getBlockEspList().entrySet()) {
            if (entry.getValue()) {
                blockList.add(entry.getKey());
            }
        }
        blockList.sort(String::compareToIgnoreCase);
    }
    
    @Override
    public void onClose() {
        this.minecraft.setScreen(this.parent);
    }
}
