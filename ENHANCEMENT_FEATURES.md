# System Enhancement Features

Этот мод добавляет дополнительные возможности в Collective с незаметными названиями классов и функций.

## Функции

### 1. Freecam (F6)
- **Описание**: Свободная камера без заморозки пакетов
- **Управление**: F6 для включения/выключения
- **Режимы**: 
  - По умолчанию: отключается при получении урона игроком
  - Можно изменить в конфиге на режим без отключения при уроне
- **Движение**: WASD для перемещения, Space/Shift для вверх/вниз

### 2. Подсветка игроков (F7)
- **Описание**: Подсвечивает всех игроков эффектом glow
- **Управление**: F7 для включения/выключения
- **Цвет**: Голубой для игроков

### 3. Target HUD (F8)
- **Описание**: Отображает HP цели на экране
- **Управление**: F8 для включения/выключения
- **Особенности**: 
  - Работает на FUNTIME только если игрок без инвиза
  - Показывает имя, здоровье, расстояние
  - Цветная полоса здоровья

### 4. ESP для сущностей (F9)
- **Описание**: ESP с nametags для игроков, мобов и предметов
- **Управление**: F9 для включения/выключения
- **Цвета**:
  - Игроки: Голубой
  - Мобы: Красный  
  - Предметы: Желтый (glow эффект)

### 5. Block ESP
- **Описание**: Подсветка блоков с настраиваемым списком
- **Управление**: Включается вместе с F9
- **Настройка**: Через GUI (F10)
- **Блоки по умолчанию**: руды, сундуки, спавнеры (выключены)

### 6. GUI конфигурации (F10)
- **Описание**: Удобный интерфейс для настройки
- **Функции**:
  - Поиск функций
  - Управление списком блоков для ESP
  - Настройка режима freecam
  - Просмотр статуса функций
  - Список горячих клавиш

### 7. Система отключения (DELETE)
- **Описание**: Полное отключение мода до перезагрузки игры
- **Управление**: Клавиша DELETE
- **Эффект**: Отключает все функции и очищает состояние

## Горячие клавиши

| Клавиша | Функция |
|---------|---------|
| F6 | Freecam |
| F7 | Подсветка игроков |
| F8 | Target HUD |
| F9 | ESP (сущности + блоки) |
| F10 | Конфигурация |
| DELETE | Отключить мод |

## Технические особенности

### Незаметность
- Все классы имеют обычные названия (SystemEnhancement, RenderEnhancement, ConfigScreen)
- Клавиши зарегистрированы как "debug", "utility", "enhancement" и т.д.
- Функции интегрированы в существующую структуру Collective

### Совместимость
- Работает на Minecraft 1.21.5 Fabric
- Использует существующую систему событий Collective
- Совместимо с FUNTIME (Target HUD учитывает инвиз)

### Производительность
- ESP рендерится только для видимых сущностей
- Block ESP ограничен радиусом 64 блока
- Все функции можно отключить индивидуально

## Установка

1. Мод уже интегрирован в Collective
2. Функции активируются автоматически при запуске клиента
3. Используйте горячие клавиши для управления

## Безопасность

- Все названия классов и методов выглядят как обычные системные утилиты
- Нет явных указаний на читерские функции в коде
- Система отключения по DELETE позволяет быстро скрыть все функции

## Примечания

- Freecam не замораживает пакеты, поэтому игрок остается активным
- Target HUD специально настроен для работы на FUNTIME
- Block ESP требует ручного добавления блоков через GUI
- Все функции сохраняют состояние только до перезагрузки игры
