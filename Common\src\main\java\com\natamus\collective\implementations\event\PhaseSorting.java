package com.natamus.collective.implementations.event;

import com.google.common.annotations.VisibleForTesting;
import com.natamus.collective.data.Constants;

import java.util.*;

/*
	Original code used from https://github.com/FabricMC/fabric.
 */
public class PhaseSorting {
	@VisibleForTesting
	public static boolean ENABLE_CYCLE_WARNING = true;

	/**
	 * Deterministically sort a list of phases.
	 * 1) Compute phase SCCs (i.e. cycles).
	 * 2) Sort phases by id within SCCs.
	 * 3) Sort SCCs with respect to each other by respecting constraints, and by id in case of a tie.
	 */
	static <T> void sortPhases(List<EventPhaseData<T>> sortedPhases) {
		// FIRST KOSARAJU SCC VISIT
		List<EventPhaseData<T>> toposort = new ArrayList<>(sortedPhases.size());

		for (EventPhaseData<T> phase : sortedPhases) {
			forwardVisit(phase, null, toposort);
		}

		clearStatus(toposort);
		Collections.reverse(toposort);

		// SECOND KOSARAJU SCC VISIT
		Map<EventPhaseData<T>, PhaseScc<T>> phaseToScc = new IdentityHashMap<>();

		for (EventPhaseData<T> phase : toposort) {
			if (phase.visitStatus == 0) {
				List<EventPhaseData<T>> sccPhases = new ArrayList<>();
				// Collect phases in SCC.
				backwardVisit(phase, sccPhases);
				// Sort phases by id.
				sccPhases.sort(Comparator.comparing(p -> p.id));
				// Mark phases as belonging to this SCC.
				PhaseScc<T> scc = new PhaseScc<>(sccPhases);

				for (EventPhaseData<T> phaseInScc : sccPhases) {
					phaseToScc.put(phaseInScc, scc);
				}
			}
		}

		clearStatus(toposort);

		// Build SCC graph
		for (PhaseScc<T> scc : phaseToScc.values()) {
			for (EventPhaseData<T> phase : scc.phases) {
				for (EventPhaseData<T> subsequentPhase : phase.subsequentPhases) {
					PhaseScc<T> subsequentScc = phaseToScc.get(subsequentPhase);

					if (subsequentScc != scc) {
						scc.subsequentSccs.add(subsequentScc);
						subsequentScc.inDegree++;
					}
				}
			}
		}

		// Order SCCs according to priorities. When there is a choice, use the SCC with the lowest id.
		// The priority queue contains all SCCs that currently have 0 in-degree.
		PriorityQueue<PhaseScc<T>> pq = new PriorityQueue<>(Comparator.comparing(scc -> scc.phases.getFirst().id));
		sortedPhases.clear();

		for (PhaseScc<T> scc : phaseToScc.values()) {
			if (scc.inDegree == 0) {
				pq.add(scc);
				// Prevent adding the same SCC multiple times, as phaseToScc may contain the same value multiple times.
				scc.inDegree = -1;
			}
		}

		while (!pq.isEmpty()) {
			PhaseScc<T> scc = pq.poll();
			sortedPhases.addAll(scc.phases);

			for (PhaseScc<T> subsequentScc : scc.subsequentSccs) {
				subsequentScc.inDegree--;

				if (subsequentScc.inDegree == 0) {
					pq.add(subsequentScc);
				}
			}
		}
	}

	private static <T> void forwardVisit(EventPhaseData<T> phase, EventPhaseData<T> parent, List<EventPhaseData<T>> toposort) {
		if (phase.visitStatus == 0) {
			// Not yet visited.
			phase.visitStatus = 1;

			for (EventPhaseData<T> data : phase.subsequentPhases) {
				forwardVisit(data, phase, toposort);
			}

			toposort.add(phase);
			phase.visitStatus = 2;
		} else if (phase.visitStatus == 1 && ENABLE_CYCLE_WARNING) {
			// Already visiting, so we have found a cycle.
			Constants.LOG.warn("Event phase ordering conflict detected.\nEvent phase {} is ordered both before and after event phase {}.", phase.id, parent.id);
		}
	}

	private static <T> void clearStatus(List<EventPhaseData<T>> phases) {
		for (EventPhaseData<T> phase : phases) {
			phase.visitStatus = 0;
		}
	}

	private static <T> void backwardVisit(EventPhaseData<T> phase, List<EventPhaseData<T>> sccPhases) {
		if (phase.visitStatus == 0) {
			phase.visitStatus = 1;
			sccPhases.add(phase);

			for (EventPhaseData<T> data : phase.previousPhases) {
				backwardVisit(data, sccPhases);
			}
		}
	}

	private static class PhaseScc<T> {
		final List<EventPhaseData<T>> phases;
		final List<PhaseScc<T>> subsequentSccs = new ArrayList<>();
		int inDegree = 0;

		private PhaseScc(List<EventPhaseData<T>> phases) {
			this.phases = phases;
		}
	}
}
