modLoader="javafml"
loaderVersion="*"
license="All rights reserved"
issueTrackerURL="https://github.com/Serilum/.issue-tracker/labels/Library:%20Collective"
logoFile="icon.png"

[[mods]]
modId="collective"
version="8.3"
displayName="Collective"
displayURL="https://serilum.com/mod/collective"
authors="Rick South"
description='''Collective is a shared library with common code for all of Serilum's mods.
'''
credits='''Thank you very much to my Membership sponsors for supporting the development of this mod:  

<PERSON><PERSON>, <PERSON><PERSON>, aternosorg, Cherriesaurus, Michael <PERSON>, t128, <PERSON>, <PERSON>Agent   

If you would like to contribute too, check out https://serilum.com/donate. Appreciate it! <3
'''

[[dependencies.collective]]
    modId="minecraft"
    type="required"
    versionRange="[1.21.5]"
    ordering="NONE"
    side="BOTH"

[[mixins]]
    config = "collective_neoforge.mixins.json"