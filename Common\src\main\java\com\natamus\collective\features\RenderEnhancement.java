package com.natamus.collective.features;

import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.core.BlockPos;
import net.minecraft.network.chat.Component;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.item.ItemEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.level.block.Block;
import net.minecraft.world.level.block.state.BlockState;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;

import java.awt.Color;

public class RenderEnhancement {
    
    public static void onHudRender(GuiGraphics guiGraphics, float partialTicks) {
        if (!SystemEnhancement.isEnabled()) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;
        
        // Render Target HUD
        if (SystemEnhancement.isTargetHudActive()) {
            renderTargetHud(guiGraphics, mc);
        }
    }
    
    public static void onWorldRender(PoseStack poseStack, MultiBufferSource bufferSource, double camX, double camY, double camZ) {
        if (!SystemEnhancement.isEnabled()) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;
        
        // Render ESP for entities
        if (SystemEnhancement.isEntityEspActive() || SystemEnhancement.isPlayerEspActive()) {
            renderEntityESP(poseStack, bufferSource, camX, camY, camZ, mc);
        }
        
        // Render Block ESP
        if (SystemEnhancement.isBlockEspActive()) {
            renderBlockESP(poseStack, bufferSource, camX, camY, camZ, mc);
        }
        
        // Render Item ESP
        if (SystemEnhancement.isItemEspActive()) {
            renderItemESP(poseStack, bufferSource, camX, camY, camZ, mc);
        }
    }
    
    private static void renderTargetHud(GuiGraphics guiGraphics, Minecraft mc) {
        Entity target = SystemEnhancement.getLastTargetedEntity();
        if (target == null || !(target instanceof LivingEntity livingTarget)) return;
        
        // Check if target is still valid and in range
        if (target.isRemoved() || mc.player.distanceTo(target) > 100) {
            return;
        }
        
        // Don't show for invisible players (FUNTIME compatibility)
        if (target instanceof Player player && player.isInvisible()) {
            return;
        }
        
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();
        
        // Position HUD in top-left corner
        int hudX = 10;
        int hudY = 10;
        
        // Background
        guiGraphics.fill(hudX - 2, hudY - 2, hudX + 150, hudY + 40, 0x80000000);
        
        // Target name
        String targetName = target.getDisplayName().getString();
        guiGraphics.drawString(mc.font, targetName, hudX, hudY, 0xFFFFFF);
        
        // Health bar
        float health = livingTarget.getHealth();
        float maxHealth = livingTarget.getMaxHealth();
        float healthPercent = health / maxHealth;
        
        int barWidth = 140;
        int barHeight = 8;
        int barY = hudY + 12;
        
        // Health bar background
        guiGraphics.fill(hudX, barY, hudX + barWidth, barY + barHeight, 0xFF333333);
        
        // Health bar fill
        int healthBarWidth = (int)(barWidth * healthPercent);
        int healthColor = getHealthColor(healthPercent);
        guiGraphics.fill(hudX, barY, hudX + healthBarWidth, barY + barHeight, healthColor);
        
        // Health text
        String healthText = String.format("%.1f/%.1f", health, maxHealth);
        guiGraphics.drawString(mc.font, healthText, hudX, barY + 12, 0xFFFFFF);
        
        // Distance
        float distance = mc.player.distanceTo(target);
        String distanceText = String.format("Distance: %.1fm", distance);
        guiGraphics.drawString(mc.font, distanceText, hudX, barY + 24, 0xAAAAAA);
    }
    
    private static void renderEntityESP(PoseStack poseStack, MultiBufferSource bufferSource, double camX, double camY, double camZ, Minecraft mc) {
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;
            
            boolean shouldRender = false;
            Color espColor = Color.WHITE;
            
            if (entity instanceof Player && SystemEnhancement.isPlayerEspActive()) {
                shouldRender = true;
                espColor = Color.CYAN;
            } else if (entity instanceof LivingEntity && SystemEnhancement.isEntityEspActive()) {
                shouldRender = true;
                espColor = Color.RED;
            } else if (entity instanceof ItemEntity && SystemEnhancement.isItemEspActive()) {
                shouldRender = true;
                espColor = Color.YELLOW;
            }
            
            if (shouldRender) {
                renderEntityBox(poseStack, bufferSource, entity, camX, camY, camZ, espColor);
                renderEntityNameTag(poseStack, bufferSource, entity, camX, camY, camZ, mc);
            }
        }
    }
    
    private static void renderBlockESP(PoseStack poseStack, MultiBufferSource bufferSource, double camX, double camY, double camZ, Minecraft mc) {
        if (SystemEnhancement.getBlockEspList().isEmpty()) return;
        
        int renderDistance = 64; // blocks
        BlockPos playerPos = mc.player.blockPosition();
        
        for (int x = -renderDistance; x <= renderDistance; x++) {
            for (int y = -renderDistance; y <= renderDistance; y++) {
                for (int z = -renderDistance; z <= renderDistance; z++) {
                    BlockPos pos = playerPos.offset(x, y, z);
                    BlockState state = mc.level.getBlockState(pos);
                    Block block = state.getBlock();
                    
                    String blockName = block.getDescriptionId().replace("block.minecraft.", "");
                    if (SystemEnhancement.getBlockEspList().containsKey(blockName) && 
                        SystemEnhancement.getBlockEspList().get(blockName)) {
                        renderBlockBox(poseStack, bufferSource, pos, camX, camY, camZ, Color.GREEN);
                    }
                }
            }
        }
    }
    
    private static void renderItemESP(PoseStack poseStack, MultiBufferSource bufferSource, double camX, double camY, double camZ, Minecraft mc) {
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof ItemEntity) {
                entity.setGlowingTag(true);
            }
        }
    }
    
    private static void renderEntityBox(PoseStack poseStack, MultiBufferSource bufferSource, Entity entity, double camX, double camY, double camZ, Color color) {
        AABB boundingBox = entity.getBoundingBox();
        
        double minX = boundingBox.minX - camX;
        double minY = boundingBox.minY - camY;
        double minZ = boundingBox.minZ - camZ;
        double maxX = boundingBox.maxX - camX;
        double maxY = boundingBox.maxY - camY;
        double maxZ = boundingBox.maxZ - camZ;
        
        VertexConsumer vertexConsumer = bufferSource.getBuffer(RenderType.lines());
        
        float r = color.getRed() / 255.0f;
        float g = color.getGreen() / 255.0f;
        float b = color.getBlue() / 255.0f;
        float a = 1.0f;
        
        // Draw bounding box lines
        drawBoxOutline(poseStack, vertexConsumer, minX, minY, minZ, maxX, maxY, maxZ, r, g, b, a);
    }
    
    private static void renderBlockBox(PoseStack poseStack, MultiBufferSource bufferSource, BlockPos pos, double camX, double camY, double camZ, Color color) {
        double minX = pos.getX() - camX;
        double minY = pos.getY() - camY;
        double minZ = pos.getZ() - camZ;
        double maxX = minX + 1.0;
        double maxY = minY + 1.0;
        double maxZ = minZ + 1.0;
        
        VertexConsumer vertexConsumer = bufferSource.getBuffer(RenderType.lines());
        
        float r = color.getRed() / 255.0f;
        float g = color.getGreen() / 255.0f;
        float b = color.getBlue() / 255.0f;
        float a = 1.0f;
        
        drawBoxOutline(poseStack, vertexConsumer, minX, minY, minZ, maxX, maxY, maxZ, r, g, b, a);
    }
    
    private static void renderEntityNameTag(PoseStack poseStack, MultiBufferSource bufferSource, Entity entity, double camX, double camY, double camZ, Minecraft mc) {
        Vec3 entityPos = entity.position();
        double x = entityPos.x - camX;
        double y = entityPos.y - camY + entity.getBbHeight() + 0.5;
        double z = entityPos.z - camZ;
        
        poseStack.pushPose();
        poseStack.translate(x, y, z);
        
        EntityRenderDispatcher dispatcher = mc.getEntityRenderDispatcher();
        poseStack.mulPose(dispatcher.cameraOrientation());
        poseStack.scale(-0.025f, -0.025f, 0.025f);
        
        Component name = entity.getDisplayName();
        int textWidth = mc.font.width(name);
        
        // Background
        VertexConsumer backgroundConsumer = bufferSource.getBuffer(RenderType.gui());
        // Text
        mc.font.drawInBatch(name, -textWidth / 2.0f, 0, 0xFFFFFF, false, poseStack.last().pose(), bufferSource, net.minecraft.client.gui.Font.DisplayMode.NORMAL, 0x40000000, 15728880);
        
        poseStack.popPose();
    }
    
    private static void drawBoxOutline(PoseStack poseStack, VertexConsumer vertexConsumer, double minX, double minY, double minZ, double maxX, double maxY, double maxZ, float r, float g, float b, float a) {
        // Bottom face
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)minZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)minZ).setColor(r, g, b, a);
        
        // Top face
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        
        // Vertical edges
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)minZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)minZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)maxX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
        
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)minY, (float)maxZ).setColor(r, g, b, a);
        vertexConsumer.addVertex(poseStack.last().pose(), (float)minX, (float)maxY, (float)maxZ).setColor(r, g, b, a);
    }
    
    private static int getHealthColor(float healthPercent) {
        if (healthPercent > 0.6f) {
            return 0xFF00FF00; // Green
        } else if (healthPercent > 0.3f) {
            return 0xFFFFFF00; // Yellow
        } else {
            return 0xFFFF0000; // Red
        }
    }
}
