package com.natamus.collective.features;


import net.minecraft.client.KeyMapping;
import net.minecraft.client.Minecraft;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import org.lwjgl.glfw.GLFW;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SystemEnhancement {
    private static boolean isEnabled = true;
    private static boolean freecamActive = false;
    private static boolean freecamDamageDisable = true;
    private static boolean playerHighlightActive = false;
    private static boolean targetHudActive = false;
    private static boolean entityEspActive = false;
    private static boolean playerEspActive = false;
    private static boolean blockEspActive = false;
    private static boolean itemEspActive = false;
    
    // Key mappings with inconspicuous names
    private static KeyMapping debugKey;
    private static KeyMapping utilityKey;
    private static KeyMapping enhancementKey;
    private static KeyMapping systemKey;
    private static KeyMapping configKey;
    private static KeyMapping disableKey;
    
    // State tracking
    private static Entity lastTargetedEntity = null;
    private static final Map<String, Boolean> blockEspList = new ConcurrentHashMap<>();
    private static double freecamX, freecamY, freecamZ;
    private static float freecamYaw, freecamPitch;
    
    public static void setKeyMappings(KeyMapping debug, KeyMapping utility, KeyMapping enhancement, KeyMapping system, KeyMapping config, KeyMapping disable) {
        debugKey = debug;
        utilityKey = utility;
        enhancementKey = enhancement;
        systemKey = system;
        configKey = config;
        disableKey = disable;
    }

    public static void initializeKeys() {
        if (!isClientSide()) return;

        // Initialize default blocks for ESP
        initializeDefaultBlocks();
    }

    private static void initializeDefaultBlocks() {
        // Add some common valuable blocks
        blockEspList.put("diamond_ore", false);
        blockEspList.put("deepslate_diamond_ore", false);
        blockEspList.put("iron_ore", false);
        blockEspList.put("deepslate_iron_ore", false);
        blockEspList.put("gold_ore", false);
        blockEspList.put("deepslate_gold_ore", false);
        blockEspList.put("emerald_ore", false);
        blockEspList.put("deepslate_emerald_ore", false);
        blockEspList.put("ancient_debris", false);
        blockEspList.put("spawner", false);
        blockEspList.put("chest", false);
        blockEspList.put("ender_chest", false);
        blockEspList.put("shulker_box", false);
    }
    
    public static void onClientTick() {
        if (!isEnabled || !isClientSide()) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return;
        
        // Handle key presses
        handleKeyInputs();
        
        // Update freecam if active
        if (freecamActive) {
            updateFreecam();
        }
        
        // Update target for HUD
        if (targetHudActive) {
            updateTargetHud();
        }
    }
    
    private static void handleKeyInputs() {
        // F6 - Toggle freecam
        if (debugKey.consumeClick()) {
            toggleFreecam();
        }
        
        // F7 - Toggle player highlight
        if (utilityKey.consumeClick()) {
            togglePlayerHighlight();
        }
        
        // F8 - Toggle target HUD
        if (enhancementKey.consumeClick()) {
            toggleTargetHud();
        }
        
        // F9 - Toggle entity ESP and block ESP
        if (systemKey.consumeClick()) {
            toggleEntityEsp();
            toggleBlockEsp();
        }
        
        // F10 - Open config GUI
        if (configKey.consumeClick()) {
            openConfigGui();
        }
        
        // DELETE - Disable mod
        if (disableKey.consumeClick()) {
            disableModUntilRestart();
        }
    }
    
    private static void toggleFreecam() {
        freecamActive = !freecamActive;
        Minecraft mc = Minecraft.getInstance();
        
        if (freecamActive && mc.player != null) {
            // Store current position
            freecamX = mc.player.getX();
            freecamY = mc.player.getY();
            freecamZ = mc.player.getZ();
            freecamYaw = mc.player.getYRot();
            freecamPitch = mc.player.getXRot();
        }
    }
    
    private static void togglePlayerHighlight() {
        playerHighlightActive = !playerHighlightActive;
        
        if (playerHighlightActive) {
            highlightAllPlayers();
        } else {
            removePlayerHighlights();
        }
    }
    
    private static void toggleTargetHud() {
        targetHudActive = !targetHudActive;
    }
    
    private static void toggleEntityEsp() {
        entityEspActive = !entityEspActive;
        playerEspActive = !playerEspActive;
        itemEspActive = !itemEspActive;
    }

    private static void toggleBlockEsp() {
        blockEspActive = !blockEspActive;
    }
    
    private static void openConfigGui() {
        Minecraft mc = Minecraft.getInstance();
        try {
            Class<?> configScreenClass = Class.forName("com.natamus.collective.features.ConfigScreen");
            Object configScreen = configScreenClass.getConstructor(net.minecraft.client.gui.screens.Screen.class)
                .newInstance(mc.screen);
            mc.setScreen((net.minecraft.client.gui.screens.Screen) configScreen);
        } catch (Exception e) {
            // Config screen not available
        }
    }
    
    private static void disableModUntilRestart() {
        isEnabled = false;
        freecamActive = false;
        playerHighlightActive = false;
        targetHudActive = false;
        entityEspActive = false;
        playerEspActive = false;
        blockEspActive = false;
        itemEspActive = false;
        
        removePlayerHighlights();
    }
    
    private static void updateFreecam() {
        // Freecam logic will be implemented in mixins
    }
    
    private static void updateTargetHud() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.crosshairPickEntity != null && mc.crosshairPickEntity instanceof LivingEntity) {
            lastTargetedEntity = mc.crosshairPickEntity;
        }
    }
    
    private static void highlightAllPlayers() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.level == null) return;
        
        for (Player player : mc.level.players()) {
            if (player != mc.player) {
                player.setGlowingTag(true);
            }
        }
    }
    
    private static void removePlayerHighlights() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.level == null) return;
        
        for (Player player : mc.level.players()) {
            player.setGlowingTag(false);
        }
    }
    
    // Getters for other classes
    public static boolean isEnabled() { return isEnabled; }
    public static boolean isFreecamActive() { return freecamActive; }
    public static boolean isFreecamDamageDisable() { return freecamDamageDisable; }
    public static boolean isPlayerHighlightActive() { return playerHighlightActive; }
    public static boolean isTargetHudActive() { return targetHudActive; }
    public static boolean isEntityEspActive() { return entityEspActive; }
    public static boolean isPlayerEspActive() { return playerEspActive; }
    public static boolean isBlockEspActive() { return blockEspActive; }
    public static boolean isItemEspActive() { return itemEspActive; }
    public static Entity getLastTargetedEntity() { return lastTargetedEntity; }
    public static Map<String, Boolean> getBlockEspList() { return blockEspList; }
    
    // Freecam position getters
    public static double getFreecamX() { return freecamX; }
    public static double getFreecamY() { return freecamY; }
    public static double getFreecamZ() { return freecamZ; }
    public static float getFreecamYaw() { return freecamYaw; }
    public static float getFreecamPitch() { return freecamPitch; }
    
    // Setters
    public static void setFreecamDamageDisable(boolean value) { freecamDamageDisable = value; }
    public static void setFreecamPosition(double x, double y, double z, float yaw, float pitch) {
        freecamX = x; freecamY = y; freecamZ = z; freecamYaw = yaw; freecamPitch = pitch;
    }
    
    private static boolean isClientSide() {
        return Minecraft.getInstance() != null;
    }
    
    // Damage check for freecam
    public static void onPlayerDamage() {
        if (freecamActive && freecamDamageDisable) {
            freecamActive = false;
        }
    }
}
