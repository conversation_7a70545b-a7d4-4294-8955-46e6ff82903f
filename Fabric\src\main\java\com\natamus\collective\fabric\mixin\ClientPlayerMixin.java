package com.natamus.collective.fabric.mixin;

import net.minecraft.client.player.LocalPlayer;
import net.minecraft.world.damagesource.DamageSource;
import org.spongepowered.asm.mixin.Mixin;
import org.spongepowered.asm.mixin.injection.At;
import org.spongepowered.asm.mixin.injection.Inject;
import org.spongepowered.asm.mixin.injection.callback.CallbackInfoReturnable;

@Mixin(value = LocalPlayer.class, priority = 1001)
public class ClientPlayerMixin {
    
    @Inject(method = "hurt", at = @At("HEAD"))
    private void onHurt(DamageSource damageSource, float f, CallbackInfoReturnable<Boolean> cir) {
        try {
            Class<?> systemEnhancementClass = Class.forName("com.natamus.collective.features.SystemEnhancement");
            systemEnhancementClass.getMethod("onPlayerDamage").invoke(null);
        } catch (Exception ignored) {
            // Enhancement features not available
        }
    }
}
